import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Plus } from 'lucide-react';
import {
  useCreateBankDetailsMutation,
  useGetBankDetailsQuery,
  useUpdateBankDetailsMutation,
  useDeleteBankDetailsMutation,
  type BankDetailsData,
  type BankDetailsUpdateData,
} from '../../store/api/apiSlice';
import bg from '../../../public/Images/bg4.png';
import { toast } from '@/utils/toast';
import ResponsiveLoader from '../Loader';

import { BankAccount as BankAccountType } from '../../types/bankAccount';
import { decryptBankDetails, encryptForAPI } from '../../utils/encryption';
import { isCompleteBankDetails } from '../../utils/bankAccountUtils';
import { useBankAccountForm } from '../../hooks/useBankAccountForm';
import BankAccountCard from '../BankAccountCard';
import BankAccountForm from '../BankAccountForm';

const BankAccount = () => {
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState<boolean>(false);
  const [activeDropdown, setActiveDropdown] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [bankAccount, setBankAccount] = useState<BankAccountType | null>(null);
  const [decryptionError, setDecryptionError] = useState<string>('');

  const ENCRYPTION_SECRET_KEY =
    import.meta.env.VITE_ENCRYPTION_SECRET_KEY ||
    'your-32-character-secret-key-here!!';

  const {
    formData,
    errors,
    handleInputChange,
    validateForm,
    resetForm,
    populateForm,
  } = useBankAccountForm();

  const {
    data: bankDetailsData,
    isLoading: isLoadingBank,
    error: bankError,
    refetch,
  } = useGetBankDetailsQuery();
  const [createBankDetails, { isLoading: isCreating }] =
    useCreateBankDetailsMutation();
  const [updateBankDetails, { isLoading: isUpdating }] =
    useUpdateBankDetailsMutation();
  const [deleteBankDetails, { isLoading: isDeleting }] =
    useDeleteBankDetailsMutation();

  useEffect(() => {
    const loadBankDetails = async () => {
      if (isLoadingBank) {
        return;
      }

      if (bankError) {
        setBankAccount(null);
        setDecryptionError('');
        return;
      }

      if (bankDetailsData?.success) {
        if (bankDetailsData.data === null) {
          setBankAccount(null);
          setDecryptionError('');
          return;
        }

        if (bankDetailsData.data) {
          try {
            setDecryptionError('');
            let decryptedData = null;

            if (bankDetailsData.data.encrypted && bankDetailsData.data.data) {
              decryptedData = await decryptBankDetails(
                bankDetailsData.data.data,
                ENCRYPTION_SECRET_KEY
              );
            } else {
              decryptedData = bankDetailsData.data;
            }

            if (isCompleteBankDetails(decryptedData)) {
              setBankAccount(decryptedData);
            } else {
              setBankAccount(null);
              setDecryptionError('');
            }
          } catch (error) {
            console.error('Error processing bank details:', error);
            setBankAccount(null);
            setDecryptionError('Error processing bank details');
          }
        } else {
          setBankAccount(null);
          setDecryptionError('');
        }
      } else {
        setBankAccount(null);
        setDecryptionError('');
      }
    };

    loadBankDetails();
  }, [bankDetailsData, bankError, isLoadingBank, ENCRYPTION_SECRET_KEY]);

  const handleFormReset = () => {
    resetForm();
    setShowForm(false);
    setIsEditing(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const bankData: BankDetailsData = {
      account_holder_name: formData.accountHolderName,
      account_no: formData.accountNumber,
      bank_name: formData.bankName,
      branch: formData.branch,
      ifsc_code: formData.ifscCode,
      address: formData.address,
      status: 'active',
    };

    try {
      let result;

      const encryptedPayload = await encryptForAPI(
        bankData,
        ENCRYPTION_SECRET_KEY
      );

      if (isEditing && bankAccount) {
        const updateData: BankDetailsUpdateData = {
          account_holder_name: formData.accountHolderName,
          account_no: formData.accountNumber,
          bank_name: formData.bankName,
          branch: formData.branch,
          ifsc_code: formData.ifscCode,
          address: formData.address,
        };
        const encryptedUpdatePayload = await encryptForAPI(
          updateData,
          ENCRYPTION_SECRET_KEY
        );
        result = await updateBankDetails(encryptedUpdatePayload).unwrap();
      } else {
        result = await createBankDetails(encryptedPayload).unwrap();
      }

      if (result.success) {
        handleFormReset();
        refetch();
        toast.success('Bank details saved successfully!');
      } else {
        toast.error('Failed to save bank details. Please try again.');
      }
    } catch (error: unknown) {
      console.error('Error saving bank details:', error);
      toast.error('Failed to save bank details. Please try again.');
    }
  };

  const handleEdit = () => {
    if (bankAccount) {
      populateForm(bankAccount);
      setIsEditing(true);
      setShowForm(true);
      setActiveDropdown(false);
    }
  };

  const handleDelete = async () => {
    if (
      window.confirm(
        'Are you sure you want to delete your bank account details? This action cannot be undone.'
      )
    ) {
      try {
        const result = await deleteBankDetails().unwrap();
        if (result.success) {
          setBankAccount(null);
          setActiveDropdown(false);
          toast.success(result.message || 'Bank details deleted successfully!');
          refetch();
        }
      } catch (error: unknown) {
        console.error('Error deleting bank details:', error);
        toast.error(
          error.data?.message ||
            'Failed to delete bank details. Please try again.'
        );
      }
    }
  };

  const handleToggleDropdown = () => {
    setActiveDropdown(!activeDropdown);
  };

  const handleAddBankAccount = () => {
    setShowForm(true);
  };

  if (isLoadingBank) {
    return <ResponsiveLoader />;
  }

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Bank Account</h1>
        </div>
      </header>

      {}
      <main className='flex-1 p-4'>
        <div className='w-11/12 md:w-11/12 mx-auto flex flex-col md:gap-0'>
          {}
          <div className='flex-shrink-0 flex justify-center mb-6'>
            <img
              src='../../../public/Images/Banking.svg'
              alt='Your Bank Account'
              className='max-w-sm w-full h-auto'
            />
          </div>

          {}
          <div className='w-full'>
            {}
            {decryptionError && (
              <div className='mb-4 p-3 bg-red-100 border border-red-300 rounded-lg'>
                <p className='text-red-700 text-sm'>{decryptionError}</p>
                <p className='text-red-600 text-xs mt-1'>
                  Please check your encryption key configuration.
                </p>
              </div>
            )}

            {}
            {bankAccount && !showForm && (
              <BankAccountCard
                bankAccount={bankAccount}
                activeDropdown={activeDropdown}
                isDeleting={isDeleting}
                onToggleDropdown={handleToggleDropdown}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            )}

            {}
            {!bankAccount && !showForm && !decryptionError && (
              <button
                onClick={handleAddBankAccount}
                className='md:w-4/12 mx-auto w-full flex items-center justify-center gap-2 py-2 bg-nursery-blue text-white rounded-lg font-medium hover:bg-nursery-darkBlue transition-colors duration-200 mb-6'
              >
                <Plus className='h-5 w-5' />
                Add Bank Account
              </button>
            )}

            {}
            {showForm && (
              <BankAccountForm
                formData={formData}
                errors={errors}
                isEditing={isEditing}
                isCreating={isCreating}
                isUpdating={isUpdating}
                onInputChange={handleInputChange}
                onSubmit={handleSubmit}
                onCancel={handleFormReset}
              />
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default BankAccount;
