import { EncryptedData, BankAccount } from '../types/bankAccount';

export class FrontendDecryption {
  private algorithm = 'aes-256-gcm';
  private secretKey: string;
  private key: CryptoKey | null = null;

  constructor(secretKey: string) {
    this.secretKey = secretKey;
  }

  private async deriveKey(): Promise<CryptoKey> {
    if (this.key) return this.key;

    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.secretKey),
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    this.key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: encoder.encode('salt'),
        iterations: 1,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['decrypt']
    );

    return this.key;
  }

  async decrypt(encryptedData: EncryptedData): Promise<BankAccount | object> {
    try {
      const key = await this.deriveKey();
      const iv = this.hexToUint8Array(encryptedData.iv);
      const authTag = this.hexToUint8Array(encryptedData.authTag);
      const encrypted = this.hexToUint8Array(encryptedData.encrypted);

      const combinedData = new Uint8Array(encrypted.length + authTag.length);
      combinedData.set(encrypted);
      combinedData.set(authTag, encrypted.length);

      const decrypted = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
          tagLength: 128,
        },
        key,
        combinedData
      );

      const decoder = new TextDecoder();
      const decryptedText = decoder.decode(decrypted);
      return JSON.parse(decryptedText);
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  private hexToUint8Array(hex: string): Uint8Array {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes;
  }
}

export class ClientEncryption {
  private algorithm = 'aes-256-gcm';
  private secretKey: string;
  private key: CryptoKey | null = null;

  constructor(secretKey: string) {
    this.secretKey = secretKey;
  }

  private async deriveKey(): Promise<CryptoKey> {
    if (this.key) return this.key;

    const encoder = new TextEncoder();
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.secretKey),
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    this.key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: encoder.encode('salt'),
        iterations: 1,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt']
    );

    return this.key;
  }

  async encrypt(data: Record<string, unknown>): Promise<EncryptedData> {
    try {
      const key = await this.deriveKey();
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encoder = new TextEncoder();
      const plaintext = encoder.encode(JSON.stringify(data));

      const encrypted = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
          tagLength: 128,
        },
        key,
        plaintext
      );

      const encryptedArray = new Uint8Array(encrypted);
      const encryptedData = encryptedArray.slice(0, -16);
      const authTag = encryptedArray.slice(-16);

      return {
        encrypted: this.uint8ArrayToHex(encryptedData),
        iv: this.uint8ArrayToHex(iv),
        authTag: this.uint8ArrayToHex(authTag),
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  private uint8ArrayToHex(bytes: Uint8Array): string {
    return Array.from(bytes)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }
}

export const encryptForAPI = async (
  data: Record<string, unknown>,
  secretKey: string
): Promise<
  { encrypted: true; data: EncryptedData } | Record<string, unknown>
> => {
  try {
    const encryption = new ClientEncryption(secretKey);
    const encryptedData = await encryption.encrypt(data);
    return {
      encrypted: true,
      data: encryptedData,
    };
  } catch (error) {
    console.error('Failed to encrypt data for API:', error);
    return data;
  }
};

function hasAccountHolderName(obj: unknown): obj is BankAccount {
  return (
    typeof obj === 'object' && obj !== null && 'account_holder_name' in obj
  );
}

const simpleDecrypt = (encryptedData: unknown): BankAccount | null => {
  try {
    if (hasAccountHolderName(encryptedData)) {
      return encryptedData;
    }
    if (typeof encryptedData === 'string') {
      return JSON.parse(encryptedData) as BankAccount;
    }
    return null;
  } catch (error) {
    console.error('Simple decrypt error:', error);
    return null;
  }
};

function isEncryptedData(obj: unknown): obj is EncryptedData {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'encrypted' in obj &&
    'iv' in obj &&
    'authTag' in obj
  );
}

export const decryptBankDetails = async (
  encryptedData: unknown,
  secretKey?: string
): Promise<BankAccount | null> => {
  try {
    const simpleResult = simpleDecrypt(encryptedData);
    if (simpleResult) {
      return simpleResult;
    }

    if (secretKey && isEncryptedData(encryptedData)) {
      const decryption = new FrontendDecryption(secretKey);
      const result = await decryption.decrypt(encryptedData);

      if (hasAccountHolderName(result)) {
        return result;
      }
      return null;
    }

    if (typeof encryptedData === 'string') {
      try {
        return JSON.parse(encryptedData) as BankAccount;
      } catch {
        return null;
      }
    }

    return null;
  } catch (error) {
    console.error('Error decrypting bank details:', error);
    return null;
  }
};
