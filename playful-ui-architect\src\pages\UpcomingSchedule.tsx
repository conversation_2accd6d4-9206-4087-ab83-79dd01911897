import React, { useState, useRef, useEffect } from 'react';
import {
  ChevronDown,
  MapPin,
  MessageCircle,
  User,
  MoreVertical,
} from 'lucide-react';
import calender from '../../public/Images/calender.svg';
import { useNavigate } from 'react-router-dom';
import { toast, toastUtils } from '@/utils/toast';
import { formatTime12Hour, formatDateLong } from '@/utils/dateTime';
import { useBookingActions } from '@/hooks/useBookingActions';
import DirectionsButton from '@/components/booking/DirectionsButton';
import DeclineBookingForm from '@/components/DeclineBookingForm';
import { useUpdateServiceStatusMutation } from '../store/api/customerApiSlice';

const UpcomingSchedule = ({
  bookingResponse,
  handleBookingStatus,
  updatingStatus,
  serviceStatus = {},
  setServiceStatus,
}) => {
  const navigate = useNavigate();

  const [activeMenuId, setActiveMenuId] = useState(null);
  const menuRef = useRef(null);

  // Add service status mutation
  const [updateServiceStatus, { isLoading: updatingServiceStatus }] =
    useUpdateServiceStatusMutation();

  const bookingActions = useBookingActions({
    onUpdateBookingStatus: handleBookingStatus,
  });

  useEffect(() => {
    const handleClickOutside = event => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setActiveMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleMenu = (bookingId, event) => {
    event.stopPropagation();
    setActiveMenuId(activeMenuId === bookingId ? null : bookingId);
  };

  const handleDeclineBooking = async (bookingId, event) => {
    if (event) {
      event.stopPropagation();
    }
    bookingActions.openDeclineForm(bookingId);
    setActiveMenuId(null);
  };

  // Start service handler
  const handleStartService = async bookingId => {
    try {
      const response = await updateServiceStatus({
        booking_id: bookingId,
        status: 'started',
      }).unwrap();

      if (response && response.success) {
        // Update local service status
        if (setServiceStatus) {
          setServiceStatus(prev => ({
            ...prev,
            [bookingId]: {
              ...prev[bookingId],
              status: 'started',
              started_at:
                response?.data?.started_at || new Date().toISOString(),
            },
          }));
        }

        toastUtils.successAlt('Service started successfully!');
      } else {
        toast.error(
          response?.message || 'Failed to start service. Please try again.'
        );
      }
    } catch (error) {
      console.error('Failed to start service:', error);
      toast.error('Failed to start service. Please try again.');
    }
  };

  // End service handler
  const handleEndService = async bookingId => {
    try {
      const response = await updateServiceStatus({
        booking_id: bookingId,
        status: 'completed',
      }).unwrap();

      if (response && response.success) {
        // Update local service status
        if (setServiceStatus) {
          setServiceStatus(prev => ({
            ...prev,
            [bookingId]: {
              ...prev[bookingId],
              status: 'completed',
              completed_at:
                response?.data?.completed_at || new Date().toISOString(),
            },
          }));
        }

        // Update booking status to completed
        try {
          await handleBookingStatus(bookingId, 'Completed');
        } catch (bookingError) {
          console.error('Failed to update booking status:', bookingError);
        }

        toastUtils.successAlt('Service completed successfully!');
      } else {
        toast.error(
          response?.message || 'Failed to complete service. Please try again.'
        );
      }
    } catch (error) {
      console.error('Failed to complete service:', error);
      toast.error('Failed to complete service. Please try again.');
    }
  };

  // Render service action button
  const renderServiceActionButton = booking => {
    if (booking.booking_status !== 'Accepted') return null;

    const currentServiceStatus =
      serviceStatus[booking.booking_id]?.status || 'not_started';

    if (currentServiceStatus === 'not_started') {
      return (
        <button
          className='bg-nursery-blue text-white hover:bg-opacity-85 disabled:opacity-50 px-3 py-1 rounded-lg font-semibold transition-colors shadow-lg mr-3'
          onClick={e => {
            e.stopPropagation();
            handleStartService(booking.booking_id);
          }}
          disabled={updatingServiceStatus}
        >
          {updatingServiceStatus ? 'Starting...' : 'Start Service'}
        </button>
      );
    }

    if (currentServiceStatus === 'started') {
      return (
        <button
          className='bg-nursery-blue text-white hover:bg-opacity-85 disabled:opacity-50 px-4 py-2 rounded-lg font-semibold transition-colors shadow-lg mr-3'
          onClick={e => {
            e.stopPropagation();
            handleEndService(booking.booking_id);
          }}
          disabled={updatingServiceStatus}
        >
          {updatingServiceStatus ? 'Ending...' : 'End Service'}
        </button>
      );
    }

    return null;
  };

  const acceptedBookings =
    bookingResponse?.bookings?.filter(booking => {
      const isAccepted = booking.booking_status === 'Accepted';
      const currentServiceStatus =
        serviceStatus[booking.booking_id]?.status || 'not_started';
      // Show bookings that are not completed or payment received
      const isNotCompleted =
        currentServiceStatus !== 'completed' &&
        currentServiceStatus !== 'payment_received';

      return isAccepted && isNotCompleted;
    }) || [];

  const sortedAcceptedBookings = [...acceptedBookings].sort(
    (a, b) =>
      new Date(b.booked_date).getTime() - new Date(a.booked_date).getTime()
  );

  const latestAcceptedBooking = sortedAcceptedBookings[0];

  const handleBookingStatusWithToast = async (
    bookingId,
    status,
    customReason
  ) => {
    try {
      await handleBookingStatus(bookingId, status, customReason);

      if (status === 'Declined') {
        toastUtils.successAlt('Booking declined successfully!');
      }

      return true;
    } catch (error) {
      console.error(`Failed to ${status.toLowerCase()} booking:`, error);

      let errorMessage = `Failed to ${status.toLowerCase()} booking`;
      if (error?.status === 'FETCH_ERROR') {
        errorMessage =
          'Network error. Please check your connection and try again.';
      } else if (error?.data?.error) {
        errorMessage = error.data.error;
      }

      toast.error(errorMessage);

      throw error;
    }
  };

  return (
    <div className='pt-6'>
      <div className='flex flex-row justify-between'>
        <div>
          <h3 className='text-xl font-semibold pb-3 text-gray-800 flex items-center'>
            Upcoming Bookings{' '}
            {acceptedBookings.length > 0 && (
              <span className='text-gray-800 rounded-full text-md font-semibold'>
                ({acceptedBookings.length})
              </span>
            )}
          </h3>
        </div>
        <div className='items-center justify-center flex'>
          {acceptedBookings.length > 0 && (
            <button
              className='flex items-center gap-2 bg-nursery-blue px-3 py-1 rounded-full mb-3'
              onClick={() => navigate('/schedule')}
            >
              <span className='text-white text-sm font-semibold'>View All</span>
              <ChevronDown className='w-4 h-4 rotate-[-90deg] text-white' />
            </button>
          )}
        </div>
      </div>

      {latestAcceptedBooking ? (
        <div className=''>
          <div className='bg-[#F2F2F2] rounded-xl gap-2 flex flex-col'>
            <div key={latestAcceptedBooking.id} className='p-4 relative'>
              <div className='flex justify-between items-center mb-3'>
                {latestAcceptedBooking.booking_status !== 'Pending' && (
                  <div className='flex'>
                    <span
                      className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        latestAcceptedBooking.booking_status === 'Accepted'
                          ? serviceStatus[latestAcceptedBooking.booking_id]
                              ?.status === 'started'
                            ? 'bg-[#f09e22] text-white'
                            : 'bg-[#00A912] text-white'
                          : latestAcceptedBooking.booking_status === 'Declined'
                            ? 'bg-[#EB001B] text-white'
                            : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {latestAcceptedBooking.booking_status === 'Accepted' &&
                      serviceStatus[latestAcceptedBooking.booking_id]
                        ?.status === 'started'
                        ? 'In Progress'
                        : latestAcceptedBooking.booking_status}
                    </span>
                  </div>
                )}

                {latestAcceptedBooking.booking_status === 'Accepted' &&
                  (serviceStatus[latestAcceptedBooking.booking_id]?.status ||
                    'not_started') === 'not_started' && (
                    <div ref={menuRef}>
                      <button
                        onClick={e =>
                          toggleMenu(latestAcceptedBooking.booking_id, e)
                        }
                        className='p-1 rounded-full hover:bg-gray-200 transition-colors'
                      >
                        <MoreVertical className='w-5 h-5 text-gray-500' />
                      </button>

                      {activeMenuId === latestAcceptedBooking.booking_id && (
                        <div className='absolute right-4 top-12 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[150px]'>
                          <button
                            onClick={e =>
                              handleDeclineBooking(
                                latestAcceptedBooking.booking_id,
                                e
                              )
                            }
                            disabled={
                              updatingStatus ||
                              bookingActions.declineForm.isSubmitting
                            }
                            className='w-full px-4 py-2 text-left text-gray-800 hover:bg-red-50 hover:text-red-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                          >
                            {updatingStatus
                              ? 'Declining...'
                              : 'Decline Booking'}
                          </button>
                        </div>
                      )}
                    </div>
                  )}
              </div>
              <div className='flex md:flex-row md:items-center flex-col md:justify-start md:gap-8 gap-1 border-b border-gray-100'>
                <div className='items-center'>
                  <div className='text-sm text-gray-800'>Booking ID</div>
                  <div className='font-semibold text-gray-900'>
                    #{latestAcceptedBooking.booking_id}
                  </div>
                </div>
                <div className='flex items-center gap-6 md:gap-7'>
                  <div>
                    <div className='text-sm text-gray-800'>Service Date</div>
                    <div className='font-semibold text-gray-900'>
                      {formatDateLong(latestAcceptedBooking.booked_date)}
                    </div>
                  </div>
                  <div>
                    <div className='text-sm text-gray-800'>Service Time</div>
                    <div className='font-semibold text-gray-900'>
                      {formatTime12Hour(latestAcceptedBooking.booked_slot)}
                    </div>
                  </div>
                </div>
                <div>
                  <div className='text-sm text-gray-800 mb-1'>Service Type</div>
                  <div className='font-semibold text-gray-900'>
                    {latestAcceptedBooking.services_selected
                      ? typeof latestAcceptedBooking.services_selected ===
                        'string'
                        ? JSON.parse(
                            latestAcceptedBooking.services_selected
                          ).join(', ')
                        : Array.isArray(latestAcceptedBooking.services_selected)
                          ? latestAcceptedBooking.services_selected.join(', ')
                          : latestAcceptedBooking.services_selected
                      : ''}
                  </div>
                </div>
              </div>
              <div className='flex md:flex-row flex-col md:justify-between items-start mt-2'>
                <div>
                  <div className='mb-2'>
                    <div className='text-sm text-gray-800 mb-2'>Patient</div>
                    <div className='flex items-center gap-3'>
                      <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
                        <User className='w-6 h-6 text-white' />
                      </div>
                      <div>
                        <div className='font-semibold text-gray-900 text-lg'>
                          {latestAcceptedBooking.customer_given_name}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className='flex items-start gap-2 mb-2'>
                      <MapPin className='w-5 h-5 text-nursery-darkBlue mt-0.5 flex-shrink-0' />
                      <div className='text-sm text-gray-800'>
                        {latestAcceptedBooking.customer_booked_location_address}
                      </div>
                    </div>
                    {latestAcceptedBooking.booking_status === 'Accepted' &&
                      (serviceStatus[latestAcceptedBooking.booking_id]
                        ?.status || 'not_started') !== 'completed' && (
                        <div className='mb-3 md:ml-0 ml-4'>
                          <DirectionsButton booking={latestAcceptedBooking} />
                        </div>
                      )}
                  </div>
                </div>
                <div className='flex flex-col md:items-end items-start w-full md:w-auto md:mt-0 px-4 '>
                  {(latestAcceptedBooking.booking_status === 'Accepted' ||
                    latestAcceptedBooking.booking_status === 'Completed') && (
                    <div className='flex items-center gap-3 mb-3'>
                      {renderServiceActionButton(latestAcceptedBooking)}
                      <button
                        className='flex items-center text-[#FF8800] bg-[#FF88002E] rounded-lg px-4 py-1 shadow-lg'
                        onClick={() => navigate('/chat')}
                      >
                        <MessageCircle className='w-8 h-8 rounded-full px-2' />
                        <span className='font-semibold'>Message</span>
                      </button>
                    </div>
                  )}
                  {latestAcceptedBooking.booking_status === 'Pending' && (
                    <div className='flex gap-3 w-full md:w-auto'>
                      <button
                        className='flex-1 bg-[#00A912] text-white hover:bg-green-700 disabled:opacity-50 px-5 py-2 rounded-lg font-semibold transition-colors'
                        onClick={() =>
                          handleBookingStatusWithToast(
                            latestAcceptedBooking.booking_id,
                            'Accepted',
                            ''
                          )
                        }
                        disabled={
                          updatingStatus ||
                          bookingActions.declineForm.isSubmitting
                        }
                      >
                        {updatingStatus ? 'Processing...' : 'Accept'}
                      </button>
                      <button
                        className='flex-1 bg-red-500 text-white hover:bg-red-600 disabled:opacity-50 px-5 py-2 rounded-lg font-semibold transition-colors'
                        onClick={e => {
                          e.preventDefault();
                          handleDeclineBooking(
                            latestAcceptedBooking.booking_id,
                            e
                          );
                        }}
                        disabled={
                          updatingStatus ||
                          bookingActions.declineForm.isSubmitting
                        }
                      >
                        {updatingStatus ? 'Processing...' : 'Decline'}
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <DeclineBookingForm
                isOpen={bookingActions.declineForm.isOpen}
                declineReason={bookingActions.declineForm.reason}
                setDeclineReason={bookingActions.updateDeclineReason}
                declineReasonError={bookingActions.declineForm.error}
                setDeclineReasonError={() => {}}
                isSubmittingDecline={bookingActions.declineForm.isSubmitting}
                onSubmit={() =>
                  bookingActions.submitDecline(latestAcceptedBooking.booking_id)
                }
                onCancel={bookingActions.closeDeclineForm}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className='bg-slate-100 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <img src={calender} alt='No Schedules' className='p-2 w-20' />
          <p className='text-base font-medium text-slate-500'>
            No upcoming schedule for you
          </p>
        </div>
      )}
    </div>
  );
};

export default UpcomingSchedule;
