import { BankAccount } from '../types/bankAccount';

export const isCompleteBankDetails = (bankDetails: BankAccount): boolean => {
  if (!bankDetails) return false;

  const requiredFields = [
    'account_holder_name',
    'account_no',
    'bank_name',
    'ifsc_code',
    'branch',
    'address',
  ];

  for (const field of requiredFields) {
    const value = bankDetails[field];
    if (
      !value ||
      typeof value !== 'string' ||
      value.trim() === '' ||
      value.toLowerCase() === 'unknown' ||
      value === 'Unknown Bank' ||
      value === 'Unknown IFSC' ||
      value.includes('****')
    ) {
      return false;
    }
  }

  if (bankDetails.account_no && bankDetails.account_no.length < 9) return false;
  if (bankDetails.ifsc_code && bankDetails.ifsc_code.length !== 11)
    return false;

  return true;
};

export const getBankIcon = (bankName: string): string => {
  if (!bankName) return 'bg-gray-500';

  const bankColors: { [key: string]: string } = {
    'State Bank of India': 'bg-blue-600',
    'SBI Bank': 'bg-blue-600',
    'HDFC Bank': 'bg-blue-500',
    'ICICI Bank': 'bg-orange-500',
    'Axis Bank': 'bg-red-600',
    'Punjab National Bank': 'bg-green-600',
    'Bank of Baroda': 'bg-pink-600',
    'Canara Bank': 'bg-purple-600',
    default: 'bg-gray-500',
  };
  return bankColors[bankName] || bankColors.default;
};

export const formatAccountNumber = (accountNo: string): string => {
  return `****${accountNo.slice(-4)}`;
};
