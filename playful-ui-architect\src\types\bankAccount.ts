import { z } from 'zod';

export const bankAccountSchema = z
  .object({
    accountHolderName: z
      .string()
      .min(1, 'Account holder name is required')
      .min(2, 'Account holder name must be at least 2 characters')
      .max(100, 'Account holder name must not exceed 100 characters')
      .regex(
        /^[a-zA-Z\s]+$/,
        'Account holder name should contain only letters and spaces'
      ),
    accountNumber: z
      .string()
      .min(9, 'Account number must be at least 9 characters')
      .max(18, 'Account number must not exceed 18 characters')
      .regex(/^\d+$/, 'Account number must contain only digits'),
    confirmAccountNumber: z
      .string()
      .min(9, 'Account number must be at least 9 characters')
      .max(18, 'Account number must not exceed 18 characters')
      .regex(/^\d+$/, 'Account number must contain only digits'),
    bankName: z
      .string()
      .min(1, 'Bank name is required')
      .min(2, 'Bank name must be at least 2 characters')
      .max(100, 'Bank name must not exceed 100 characters'),
    branch: z
      .string()
      .min(1, 'Branch is required')
      .min(2, 'Branch must be at least 2 characters')
      .max(100, 'Branch must not exceed 100 characters'),
    ifscCode: z
      .string()
      .length(11, 'IFSC code must be exactly 11 characters')
      .regex(
        /^[A-Z]{4}0[A-Z0-9]{6}$/,
        'IFSC code format: First 4 letters, then 0, then 6 alphanumeric'
      ),
    address: z
      .string()
      .min(1, 'Address is required')
      .min(10, 'Address must be at least 10 characters')
      .max(500, 'Address must not exceed 500 characters'),
    confirmTerms: z
      .boolean()
      .refine(val => val === true, 'Please confirm to proceed'),
  })
  .refine(data => data.accountNumber === data.confirmAccountNumber, {
    message: "Account numbers don't match",
    path: ['confirmAccountNumber'],
  });

export interface BankAccount {
  id?: number;
  cognito_id?: string;
  account_holder_name: string;
  account_no: string;
  bank_name: string;
  branch: string;
  ifsc_code: string;
  address: string;
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
}

export interface BankAccountForm {
  accountHolderName: string;
  accountNumber: string;
  confirmAccountNumber: string;
  bankName: string;
  branch: string;
  ifscCode: string;
  address: string;
  confirmTerms: boolean;
}

export interface ValidationErrors {
  [key: string]: string;
}

export interface EncryptedData {
  encrypted: string;
  iv: string;
  authTag: string;
}
