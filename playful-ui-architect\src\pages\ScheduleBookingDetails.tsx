import React, { useEffect } from 'react';
import { useGetServiceStatusByBookingIdQuery } from '../store/api/customerApiSlice';
import { CheckCircle2 } from 'lucide-react';
import { toast } from '@/utils/toast';

function ScheduleBookingDetails({ booking, isExpanded, serviceStatus }) {
  const {
    data: serviceStatusData,
    isLoading: _loadingServiceStatus,
    error: serviceStatusError,
  } = useGetServiceStatusByBookingIdQuery(booking.booking_id, {
    skip: !booking.booking_id || booking.booking_status !== 'Completed',
  });

  useEffect(() => {
    if (serviceStatusError) {
      console.error('Failed to load service status:', serviceStatusError);

      if (
        serviceStatusError?.status === 400 &&
        serviceStatusError?.data?.message === 'Booking ID is required'
      ) {
        toast.error('Booking ID is missing. Please refresh and try again.');
      } else if (
        serviceStatusError?.status === 400 &&
        serviceStatusError?.data?.message === 'Invalid booking ID format'
      ) {
        toast.error('Invalid booking ID format. Please contact support.');
      } else if (
        serviceStatusError?.status === 404 ||
        serviceStatusError?.data?.message?.includes('not found')
      ) {
        toast.error('Service status not found for this booking.');
      } else if (
        serviceStatusError?.status === 403 ||
        serviceStatusError?.data?.message?.includes('Unauthorized')
      ) {
        toast.error(
          "Access denied. You don't have permission to view this service status."
        );
      } else if (serviceStatusError?.status === 500) {
        toast.error('Failed to load service status. Please try again later.');
      } else if (serviceStatusError?.status === 'FETCH_ERROR') {
        toast.error(
          'Network error loading service status. Please check your connection.'
        );
      } else {
        toast.error('Failed to load service status. Please try refreshing.');
      }
    }
  }, [serviceStatusError]);

  const getCurrentServiceStatus = bookingId => {
    if (serviceStatusData?.success && serviceStatusData?.data) {
      return serviceStatusData.data.status;
    }
    if (serviceStatus[bookingId]?.status) {
      return serviceStatus[bookingId].status;
    }
    if (booking.booking_status === 'Completed') {
      return 'completed';
    }
    return 'not_started';
  };

  const getServiceStatusData = bookingId => {
    if (serviceStatusData?.success && serviceStatusData?.data) {
      return serviceStatusData.data;
    }
    return {
      started_at: null,
      completed_at: null,
      payment_received_at: null,
      status: 'not_started',
      ...serviceStatus[bookingId],
    };
  };

  const _formatTimestamp = (timestamp: string) => {
    if (!timestamp) return null;

    const date = new Date(timestamp);

    if (isNaN(date.getTime())) return null;

    return date;
  };

  const formatDateShort = timestamp => {
    if (!timestamp) return null;
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return null;

    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
    });
  };

  const formatTime = timeString => {
    if (!timeString) return null;
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const currentServiceStatus = getCurrentServiceStatus(booking.booking_id);
  const serviceStatusDataObj = getServiceStatusData(booking.booking_id);

  return (
    <div
      className={`overflow-hidden transition-all duration-500 ease-in-out ${
        isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-0 opacity-0'
      }`}
    >
      <div className='mt-4 border-t border-slate-400 pt-4'>
        <div className='bg-white rounded-lg p-4 shadow-sm'>
          <div className='mb-4'>
            <div className='space-y-0'>
              {}
              <div className='flex'>
                <div className='relative flex flex-col items-center'>
                  <div className='w-6 h-6 bg-nursery-blue rounded-full flex items-center justify-center z-10'>
                    <CheckCircle2 className='w-4 h-4 text-white' />
                  </div>
                  {}
                  {(currentServiceStatus === 'started' ||
                    currentServiceStatus === 'completed' ||
                    currentServiceStatus === 'payment_received') && (
                    <div className='absolute top-6 h-full w-0.5 bg-nursery-blue'></div>
                  )}
                </div>
                <div className='ml-3 flex-1'>
                  <div className='font-semibold text-gray-800 text-base'>
                    Booking Confirmed
                  </div>
                  <div className='text-sm text-gray-500 mb-1'>
                    You have Accepted the Booking
                  </div>
                  {booking.created_at && (
                    <div className='flex items-center gap-2 mb-3'>
                      <p className='font-medium text-gray-700'>
                        {formatDateShort(booking.created_at)}
                      </p>
                      <p className='text-sm font-medium text-gray-700'>
                        {formatTime(
                          new Date(
                            new Date(booking.created_at).getTime() +
                              5.5 * 60 * 60 * 1000
                          )
                            .toTimeString()
                            .substring(0, 5)
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {}
              <div
                className={`flex mt-6 ${
                  currentServiceStatus === 'started' ||
                  currentServiceStatus === 'completed' ||
                  currentServiceStatus === 'payment_received'
                    ? ''
                    : 'opacity-50'
                }`}
              >
                <div className='relative flex flex-col items-center'>
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center z-10 ${
                      currentServiceStatus === 'started' ||
                      currentServiceStatus === 'completed' ||
                      currentServiceStatus === 'payment_received'
                        ? 'bg-nursery-blue'
                        : 'bg-gray-300'
                    }`}
                  >
                    <CheckCircle2 className='w-4 h-4 text-white' />
                  </div>
                  {}
                  {(currentServiceStatus === 'completed' ||
                    currentServiceStatus === 'payment_received') && (
                    <div className='absolute top-6 h-full w-0.5 bg-nursery-blue'></div>
                  )}
                </div>
                <div className='ml-3 flex-1'>
                  <div className='font-semibold text-gray-800 text-base'>
                    Service Started
                  </div>
                  <div className='text-sm text-gray-500 mb-1'>
                    Nurse started providing the services
                  </div>
                  {serviceStatusDataObj.started_at && (
                    <div className='flex items-center gap-2 mb-3'>
                      <p className='font-medium text-gray-700'>
                        {formatDateShort(serviceStatusDataObj.started_at)}
                      </p>
                      <p className='text-sm font-medium text-gray-700'>
                        {formatTime(
                          new Date(serviceStatusDataObj.started_at)
                            .toTimeString()
                            .substring(0, 5)
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {}
              <div
                className={`flex mt-6 ${
                  currentServiceStatus === 'completed' ||
                  currentServiceStatus === 'payment_received'
                    ? ''
                    : 'opacity-50'
                }`}
              >
                <div className='relative flex flex-col items-center'>
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center z-10 ${
                      currentServiceStatus === 'completed' ||
                      currentServiceStatus === 'payment_received'
                        ? 'bg-nursery-blue'
                        : 'bg-gray-300'
                    }`}
                  >
                    <CheckCircle2 className='w-4 h-4 text-white' />
                  </div>
                  {}
                  {currentServiceStatus === 'payment_received' && (
                    <div className='absolute top-6 h-full w-0.5 bg-nursery-blue'></div>
                  )}
                </div>
                <div className='ml-3 flex-1'>
                  <div className='font-semibold text-gray-800 text-base'>
                    Service Completed
                  </div>
                  <div className='text-sm text-gray-500 mb-1'>
                    Service completed successfully
                  </div>
                  {serviceStatusDataObj.completed_at && (
                    <div className='flex items-center gap-2 mb-3'>
                      <p className='font-medium text-gray-700'>
                        {formatDateShort(serviceStatusDataObj.completed_at)}
                      </p>
                      <p className='text-sm font-medium text-gray-700'>
                        {formatTime(
                          new Date(serviceStatusDataObj.completed_at)
                            .toTimeString()
                            .substring(0, 5)
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {}
              <div
                className={`flex mt-6 ${
                  currentServiceStatus === 'payment_received'
                    ? ''
                    : 'opacity-50'
                }`}
              >
                <div className='relative  flex flex-col items-center'>
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center z-10 ${
                      currentServiceStatus === 'payment_received'
                        ? 'bg-nursery-blue'
                        : 'bg-gray-300'
                    }`}
                  >
                    <CheckCircle2 className='w-4 h-4 text-white' />
                  </div>
                </div>
                <div className='ml-3 flex-1'>
                  <div className='font-semibold text-gray-800 text-base'>
                    Transaction Completed
                  </div>
                  <div className='text-sm text-gray-500 mb-1'>
                    Patient Made the Payment
                  </div>
                  {serviceStatusDataObj.payment_received_at && (
                    <div className='flex items-center gap-2 mb-3'>
                      <p className='font-medium text-gray-700'>
                        {formatDateShort(
                          serviceStatusDataObj.payment_received_at
                        )}
                      </p>
                      <p className='text-sm font-medium text-gray-700'>
                        {formatTime(
                          new Date(serviceStatusDataObj.payment_received_at)
                            .toTimeString()
                            .substring(0, 5)
                        )}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ScheduleBookingDetails;
