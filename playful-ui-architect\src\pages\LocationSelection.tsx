import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from '@/utils/toast';
import { useUpdateNurseLocationMutation } from '@/store/api/apiSlice';
import { AlertCircle, MapPin, Search, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import bg from '/Images/bg4.png';

const containerStyle = {
  width: '100%',
  height: '400px',
};

const getNetworkLocation = async (): Promise<{ lat: number; lng: number }> => {
  try {
    const response = await fetch('https://ipapi.co/json/');
    const data = await response.json();
    return {
      lat: data.latitude,
      lng: data.longitude,
    };
  } catch (error) {
    console.error('Error fetching network location:', error);

    return {
      lat: 20.5937,
      lng: 78.9629,
    };
  }
};

const defaultCenter = {
  lat: 20.5937,
  lng: 78.9629,
};

const libraries: 'places'[] = ['places'];

// Helper function to check if Google Maps API is fully loaded
const isGoogleMapsApiLoaded = (): boolean => {
  return !!(
    window.google &&
    window.google.maps &&
    window.google.maps.places &&
    window.google.maps.Geocoder &&
    window.google.maps.places.AutocompleteService &&
    window.google.maps.places.PlacesService
  );
};

const LocationSelection = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [updateLocation, { isLoading }] = useUpdateNurseLocationMutation();
  const [marker, setMarker] = useState(defaultCenter);
  const [address, setAddress] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [geoError, setGeoError] = useState<string | null>(null);
  const [_isLoadingLocation, setIsLoadingLocation] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [isGoogleApiReady, setIsGoogleApiReady] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const geocoderRef = useRef<google.maps.Geocoder | null>(null);
  const autocompleteServiceRef =
    useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceRef = useRef<google.maps.places.PlacesService | null>(
    null
  );
  const confirmButtonRef = useRef<HTMLButtonElement>(null);
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize Google Maps services when API is ready
  const initializeGoogleServices = useCallback(() => {
    if (!isGoogleMapsApiLoaded()) {
      return false;
    }

    try {
      geocoderRef.current = new window.google.maps.Geocoder();
      autocompleteServiceRef.current = new window.google.maps.places.AutocompleteService();

      if (mapRef.current) {
        placesServiceRef.current = new window.google.maps.places.PlacesService(mapRef.current);
      }

      setIsGoogleApiReady(true);
      return true;
    } catch (error) {
      console.error('Error initializing Google Maps services:', error);
      return false;
    }
  }, []);

  // Initialize location on component mount
  useEffect(() => {
    const initializeLocation = async () => {
      try {
        const networkLocation = await getNetworkLocation();
        setMarker(networkLocation);

        // Only try to reverse geocode if Google API is ready
        if (isGoogleMapsApiLoaded()) {
          reverseGeocode(networkLocation);
        }
      } catch (error) {
        console.error('Error initializing location:', error);
      }
    };

    initializeLocation();
  }, []);

  // Initialize Google services when map loads and API is ready
  useEffect(() => {
    if (isMapLoaded) {
      const success = initializeGoogleServices();

      if (success && marker.lat !== defaultCenter.lat && marker.lng !== defaultCenter.lng) {
        // Re-run reverse geocoding if we have a custom location but no address yet
        if (!address) {
          reverseGeocode(marker);
        }
      }

      // Set up a retry mechanism if services aren't ready yet
      if (!success) {
        initializationTimeoutRef.current = setTimeout(() => {
          initializeGoogleServices();
        }, 1000);
      }
    }

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }
    };
  }, [isMapLoaded, initializeGoogleServices, marker, address]);

  // Handle search suggestions with proper API readiness check
  useEffect(() => {
    if (searchQuery && isGoogleApiReady && autocompleteServiceRef.current) {
      setIsSearching(true);
      autocompleteServiceRef.current.getPlacePredictions(
        {
          input: searchQuery,
          componentRestrictions: { country: 'in' },
        },
        (predictions, status) => {
          setIsSearching(false);
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            predictions
          ) {
            setSearchSuggestions(predictions);
            setShowSuggestions(true);
          } else {
            setSearchSuggestions([]);
            setShowSuggestions(false);
          }
        }
      );
    } else {
      setSearchSuggestions([]);
      setShowSuggestions(false);
      setIsSearching(false);
    }
  }, [searchQuery, isGoogleApiReady]);

  useEffect(() => {
    setIsLoadingLocation(true);
    setGeoError(null);

    const successHandler = (position: GeolocationPosition) => {
      const pos = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setMarker(pos);
      reverseGeocode(pos);
      setIsLoadingLocation(false);
    };

    const errorHandler = (error: GeolocationPositionError) => {
      let errorMessage = 'Unable to retrieve your location.';

      switch (error.code) {
        case error.PERMISSION_DENIED:
          errorMessage =
            'Location access was denied. Please enable location services in your browser settings or use the search box.';
          break;
        case error.POSITION_UNAVAILABLE:
          errorMessage =
            'Location information is unavailable. Please use the search box to find your location.';
          break;
        case error.TIMEOUT:
          errorMessage =
            'The request to get your location timed out. Please use the search box to find your location.';
          break;
      }

      setGeoError(errorMessage);
      setIsLoadingLocation(false);

      if (error.code !== error.TIMEOUT) {
        toast.error(errorMessage);
      }
    };

    const options = {
      enableHighAccuracy: true,
      timeout: 60000,
      maximumAge: 0,
    };

    navigator.geolocation.getCurrentPosition(
      successHandler,
      errorHandler,
      options
    );
  }, []);

  const reverseGeocode = useCallback((position: google.maps.LatLngLiteral) => {
    if (!isGoogleMapsApiLoaded()) {
      console.warn('Google Maps API not ready for reverse geocoding');
      return;
    }

    if (!geocoderRef.current) {
      try {
        geocoderRef.current = new window.google.maps.Geocoder();
      } catch (error) {
        console.error('Failed to create Geocoder:', error);
        return;
      }
    }

    geocoderRef.current.geocode({ location: position }, (results, status) => {
      if (status === 'OK' && results?.[0]) {
        setAddress(results[0].formatted_address);
      } else {
        console.error('Geocoding failed:', status);
        // Fallback: set a basic address format
        setAddress(`${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
      }
    });
  }, []);

  const scrollToConfirmButton = () => {
    if (confirmButtonRef.current) {
      confirmButtonRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  const handleMapClick = useCallback((e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newPosition = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };
      setMarker(newPosition);
      reverseGeocode(newPosition);
    }
  }, []);

  const handleMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    setIsMapLoaded(true);

    // Initialize services immediately if API is ready
    if (isGoogleMapsApiLoaded()) {
      try {
        geocoderRef.current = new window.google.maps.Geocoder();
        placesServiceRef.current = new window.google.maps.places.PlacesService(map);
        autocompleteServiceRef.current = new window.google.maps.places.AutocompleteService();
        setIsGoogleApiReady(true);
      } catch (error) {
        console.error('Error initializing Google Maps services on map load:', error);
      }
    }
  }, []);

  const handleMarkerDragEnd = (e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newPosition = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };
      setMarker(newPosition);
      reverseGeocode(newPosition);
    }
  };

  const handleSuggestionClick = useCallback((placeId: string) => {
    if (!isGoogleApiReady || !placesServiceRef.current) {
      toast.error('Maps service not ready. Please try again in a moment.');
      return;
    }

    placesServiceRef.current.getDetails(
      {
        placeId: placeId,
        fields: ['geometry', 'formatted_address', 'name'],
      },
      (place, status) => {
        if (
          status === window.google.maps.places.PlacesServiceStatus.OK &&
          place &&
          place.geometry?.location
        ) {
          const newPosition = {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
          };
          setMarker(newPosition);
          setAddress(place.formatted_address || '');
          setSearchQuery('');
          setShowSuggestions(false);

          if (mapRef.current) {
            mapRef.current.panTo(newPosition);
            mapRef.current.setZoom(15);
          }

          setTimeout(() => {
            scrollToConfirmButton();
          }, 800);
        } else {
          toast.error('Failed to get place details. Please try again.');
        }
      }
    );
  }, [isGoogleApiReady]);

  const handleSearch = useCallback(() => {
    if (!searchQuery) {
      toast.error('Please enter a location to search.');
      return;
    }

    if (!isGoogleApiReady || !geocoderRef.current) {
      toast.error('Maps service not ready. Please try again in a moment.');
      return;
    }

    setIsSearching(true);
    geocoderRef.current.geocode(
      { address: searchQuery },
      (results, status) => {
        setIsSearching(false);
        if (status === 'OK' && results?.[0]) {
          const location = results[0].geometry.location;
          const newPosition = {
            lat: location.lat(),
            lng: location.lng(),
          };
          setMarker(newPosition);
          setAddress(results[0].formatted_address);
          setSearchQuery('');
          setShowSuggestions(false);

          if (mapRef.current) {
            mapRef.current.panTo(newPosition);
            mapRef.current.setZoom(15);
          }

          setTimeout(() => {
            scrollToConfirmButton();
          }, 800);
        } else {
          toast.error('Location not found. Please try a different search.');
        }
      }
    );
  }, [searchQuery, isGoogleApiReady]);

  const handleSubmit = async () => {
    if (!address) {
      toast.error('Please select a location on the map');
      return;
    }
    const token =
      localStorage.getItem('idToken') || localStorage.getItem('token');

    if (!token) {
      toast.error('You need to log in first. Redirecting to login page...');
      navigate('/login');
      return;
    }
    try {
      const result = await updateLocation({
        latitude: marker.lat,
        longitude: marker.lng,
        address,
      }).unwrap();

      if (result.message === 'Location updated successfully') {
        toast.success('Location updated successfully!');
        navigate('/home', {
          state: {
            given_name: result.user?.given_name,
            username: result.user?.username,
            address: result.user?.address,
            nurse_onboarding_completed:
              result.user?.nurse_onboard_complete ||
              location.state?.nurse_onboarding_completed,
          },
        });
      }
    } catch (error) {
      toast.error('Failed to update location. Please try again.');
      console.error('Location update error:', error);

      const errorMessage = error?.data?.error || error?.message;
      const statusCode = error?.status;

      switch (statusCode) {
        case 404:
          if (errorMessage?.includes('User not found')) {
            toast.error('User not found. Please log in again.');
            localStorage.removeItem('idToken');
            navigate('/login');
          } else {
            toast.error('Failed to update user location');
          }
          break;
        case 403:
          toast.error('Access denied. Please log in again.');
          localStorage.removeItem('idToken');
          navigate('/login');
          break;
        case 400:
          if (errorMessage?.includes('Latitude')) {
            toast.error(
              'Invalid location coordinates. Please select a valid location.'
            );
          } else if (errorMessage?.includes('Longitude')) {
            toast.error(
              'Invalid location coordinates. Please select a valid location.'
            );
          } else {
            toast.error(
              errorMessage || 'Invalid location data. Please try again.'
            );
          }
          break;
        case 500:
          toast.error('Server error. Please try again later.');
          break;
        default:
          if (error?.name === 'NetworkError' || !navigator.onLine) {
            toast.error('Network error. Please check your connection.');
          } else {
            toast.error('Failed to update location. Please try again.');
          }
      }
    }
  };

  return (
    <div className='min-h-screen flex flex-col relative p-4'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src={bg}
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>
      {}
      <div className='absolute inset-0 bg-black bg-opacity-20 z-0' />

      <div className='flex-1 flex flex-col items-center justify-start pt-8 z-10'>
        <img
          src='/Images/Logo.svg'
          alt='Nurses team'
          className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mx-auto mb-3 -mt-8'
        />

        <Card className='w-full max-w-2xl bg-white rounded-md p-6 shadow-lg'>
          <h2 className='text-2xl font-bold text-center mb-6 text-gray-800'>
            Select Your Location
          </h2>

          {geoError && (
            <Alert variant='destructive' className='mb-4'>
              <AlertCircle className='h-4 w-4' />
              <AlertTitle>Location Error</AlertTitle>
              <AlertDescription>{geoError}</AlertDescription>
            </Alert>
          )}

          <div className='space-y-6'>
            {}
            <div className='relative'>
              <div className='relative'>
                <Input
                  ref={searchInputRef}
                  type='text'
                  placeholder={
                    isGoogleApiReady
                      ? 'Search for a location or enter an address'
                      : 'Loading maps... Please wait'
                  }
                  className='w-full pr-12 h-10 text-base border-2 border-gray-300 focus:border-200 transition-all duration-200'
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                  disabled={!isGoogleApiReady}
                />
                <Button
                  type='button'
                  size='icon'
                  variant='ghost'
                  className='absolute right-1 top-1 h-10 w-10 hover:bg-gray-100 transition-colors duration-200'
                  disabled={isSearching || !isGoogleApiReady}
                  onClick={handleSearch}
                >
                  {isSearching ? (
                    <Loader2 className='h-5 w-5 animate-spin text-gray-600' />
                  ) : (
                    <Search className='h-5 w-5 text-gray-600' />
                  )}
                </Button>
              </div>

              {}
              {showSuggestions && searchSuggestions.length > 0 && (
                <div className='absolute z-50 w-full mt-2 bg-white border-2 border-gray-200 rounded-lg shadow-xl max-h-64 overflow-y-auto'>
                  {searchSuggestions.map((suggestion, index) => (
                    <div
                      key={suggestion.place_id}
                      className={`px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-start transition-colors duration-150 ${
                        index !== searchSuggestions.length - 1
                          ? 'border-b border-gray-100'
                          : ''
                      }`}
                      onClick={() => handleSuggestionClick(suggestion.place_id)}
                    >
                      <MapPin className='h-5 w-5 mt-0.5 mr-3 flex-shrink-0 text-gray-500' />
                      <div className='flex-1 min-w-0'>
                        <div className='text-sm font-medium text-gray-900 truncate'>
                          {suggestion.structured_formatting?.main_text}
                        </div>
                        <div className='text-xs text-gray-500 mt-1 truncate'>
                          {suggestion.structured_formatting?.secondary_text}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {geoError && (
                <p className='text-sm text-gray-600 mt-2 bg-blue-50 p-3 rounded-md border border-blue-200'>
                  💡 Tip: Enter your address in the search box above to find
                  your location.
                </p>
              )}
            </div>

            {}
            <div className='relative border-2 border-gray-200 rounded-lg overflow-hidden'>
              {!isMapLoaded && (
                <Skeleton className='w-full h-[400px] absolute top-0 left-0 z-10 bg-gray-200' />
              )}
              <LoadScript
                googleMapsApiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}
                libraries={libraries}
                onLoad={() => {
                  console.log('Google Maps API loaded successfully');
                }}
                onError={(error) => {
                  console.error('Error loading Google Maps API:', error);
                  toast.error('Failed to load Google Maps. Please refresh the page.');
                }}
                loadingElement={
                  <Skeleton className='w-full h-[400px] bg-gray-200' />
                }
              >
                <GoogleMap
                  mapContainerStyle={containerStyle}
                  center={marker}
                  zoom={15}
                  onClick={handleMapClick}
                  onLoad={handleMapLoad}
                  options={{
                    zoomControl: true,
                    mapTypeControl: true,
                    scaleControl: true,
                    streetViewControl: true,
                    rotateControl: true,
                    fullscreenControl: true,
                    styles: [
                      {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'on' }],
                      },
                    ],
                  }}
                >
                  <Marker
                    position={marker}
                    draggable={true}
                    onDragEnd={handleMarkerDragEnd}
                  />
                </GoogleMap>
              </LoadScript>
            </div>

            {}
            <div className='space-y-2'>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Selected Address
              </label>
              <Input
                type='text'
                value={address}
                readOnly
                placeholder='Click on the map or search to select location'
                className='w-full h-12 text-base bg-gray-50 border-2 border-gray-200 text-gray-800 font-medium'
              />
            </div>

            {}
            <Button
              ref={confirmButtonRef}
              onClick={handleSubmit}
              className='w-full h-12 bg-[#4AB4CE] hover:bg-[#3a9bb3] text-white font-medium text-base transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
              disabled={isLoading || !address}
            >
              {isLoading ? (
                <>
                  <Loader2 className='h-5 w-5 animate-spin mr-2' />
                  Updating...
                </>
              ) : (
                'Confirm Location'
              )}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LocationSelection;
