import { useState } from 'react';
import { BankAccount } from '../types/bankAccount';
import { z } from 'zod';
import {
  BankAccountForm,
  ValidationErrors,
  bankAccountSchema,
} from '../types/bankAccount';

export const useBankAccountForm = () => {
  const [formData, setFormData] = useState<BankAccountForm>({
    accountHolderName: '',
    accountNumber: '',
    confirmAccountNumber: '',
    bankName: '',
    branch: '',
    ifscCode: '',
    address: '',
    confirmTerms: false,
  });

  const [errors, setErrors] = useState<ValidationErrors>({});

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    let processedValue = value;

    if (name === 'ifscCode') {
      processedValue = value.toUpperCase().slice(0, 11);
    }

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : processedValue,
    }));
  };

  const validateForm = (): boolean => {
    try {
      bankAccountSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: ValidationErrors = {};
        error.errors.forEach(err => {
          if (err.path) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const resetForm = () => {
    setFormData({
      accountHolderName: '',
      accountNumber: '',
      confirmAccountNumber: '',
      bankName: '',
      branch: '',
      ifscCode: '',
      address: '',
      confirmTerms: false,
    });
    setErrors({});
  };

  const populateForm = (bankAccount: BankAccount) => {
    setFormData({
      accountHolderName: bankAccount.account_holder_name,
      accountNumber: bankAccount.account_no,
      confirmAccountNumber: bankAccount.account_no,
      bankName: bankAccount.bank_name,
      branch: bankAccount.branch,
      ifscCode: bankAccount.ifsc_code,
      address: bankAccount.address,
      confirmTerms: true,
    });
  };

  return {
    formData,
    errors,
    handleInputChange,
    validateForm,
    resetForm,
    populateForm,
    setFormData,
    setErrors,
  };
};
