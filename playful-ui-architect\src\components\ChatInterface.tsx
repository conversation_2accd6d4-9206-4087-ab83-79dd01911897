import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
  useReducer,
} from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import useWebSocketChat from '@/hooks/useWebSocketChat';
import { useMarkAsRead } from '@/hooks/useMarkAsRead';
import { useToast } from '@/hooks/use-toast';
import { format, isValid } from 'date-fns';
import { WebSocketStatus } from '@/hooks/useWebSocket';
import { Message } from '@/store/api/chatApiSlice';

export interface PatientInfo {
  customer_cognitoId: string;
  customer_given_name: string;
}

export interface ChatInterfaceProps {
  selectedPatient?: PatientInfo | null;
  isOpen?: boolean;
  onClose?: () => void;

  mode?: 'modal' | 'fullscreen';

  children: (chatState: ChatState) => React.ReactNode;
}

export interface ChatState {
  newMessage: string;
  setNewMessage: (message: string) => void;
  isTyping: boolean;
  conversationId: string | null;
  patientInfo: PatientInfo | null;

  wsStatus: WebSocketStatus;
  currentMessages: Message[];
  typingUsers: Record<
    string,
    { userId: string; userName: string; timestamp: number }
  >;

  isLoading: boolean;
  isLoadingMessages: boolean;
  isCreatingConversation: boolean;
  isSendingMessage: boolean;

  error: string | null;

  handleSendMessage: () => Promise<void>;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleClose: () => void;
  handleBack: () => void;

  safeFormatDate: (
    timestamp: string | undefined,
    formatString: string,
    fallback?: string
  ) => string;

  messagesEndRef: React.RefObject<HTMLDivElement>;

  userId: string;
  userGivenName: string;
  displayPatientName: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  selectedPatient,
  isOpen = true,
  onClose,
  mode = 'modal',
  children,
}) => {
  const { conversationId: routeConversationId } = useParams<{
    conversationId: string;
  }>();
  const location = useLocation();
  const navigate = useNavigate();

  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [conversationId, setConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousConversationIdRef = useRef<string | null>(null);
  const { toast } = useToast();

  const patientInfo = useMemo(
    () =>
      mode === 'fullscreen'
        ? {
            customer_cognitoId: location.state?.patientId || '',
            customer_given_name: location.state?.patientName || 'Patient',
          }
        : selectedPatient,
    [
      mode,
      location.state?.patientId,
      location.state?.patientName,
      selectedPatient,
    ]
  );

  const effectiveConversationId =
    mode === 'fullscreen' ? routeConversationId : conversationId;

  const safeFormatDate = (
    timestamp: string | undefined,
    formatString: string,
    fallback: string = '--'
  ) => {
    if (!timestamp) return fallback;

    const date = new Date(timestamp);
    if (!isValid(date)) return fallback;

    try {
      return format(date, formatString);
    } catch (error) {
      console.error('Error formatting date:', error);
      return fallback;
    }
  };

  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Nurse';
  const userType = 'nurse';

  const baseWsUrl =
    import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws')
    ? baseWsUrl
    : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  const webSocketEnabled = !!(token && userId);

  const webSocketChat = useWebSocketChat({
    url: wsUrl,
    token,
    userId,
    userType,
    userName: userGivenName,
    enabled: webSocketEnabled,
  });

  const {
    status: wsStatus,
    conversations: _conversations,
    messages: wsMessages,
    loading,
    errors,
    getConversation: _getConversation,
    createConversation,
    getMessages,
    sendMessage,
    joinConversation,
    leaveConversation,
    sendTypingIndicator,
    sendReadReceipt,
    typingUsers,
    lastUpdate,
    messageCounter,
  } = webSocketChat;

  const markAsReadHook = useMarkAsRead({
    conversationId: effectiveConversationId,
    userId,
    debounceMs: 1000,
    sendReadReceipt,
    markMessagesAsRead: webSocketChat.markMessagesAsRead,
  });

  const currentMessages = useMemo(() => {
    const messages = effectiveConversationId
      ? wsMessages[effectiveConversationId] || []
      : [];
    return messages;
  }, [effectiveConversationId, wsMessages]);

  const [, forceUpdate] = useReducer(x => x + 1, 0);

  useEffect(() => {
    if (effectiveConversationId && wsMessages[effectiveConversationId]) {
      forceUpdate();
    }
  }, [effectiveConversationId, wsMessages, lastUpdate, messageCounter]);

  const isLoadingMessages =
    loading[`messages-${effectiveConversationId}`] || false;
  const isCreatingConversation = loading['create-conversation'] || false;
  const isSendingMessage =
    loading[`send-message-${effectiveConversationId}`] || false;

  useEffect(() => {
    if (
      mode === 'modal' &&
      patientInfo &&
      isOpen &&
      !conversationId &&
      wsStatus.connected
    ) {
      const createConversationForPatient = async () => {
        try {
          const response = await createConversation({
            patientId: patientInfo.customer_cognitoId,
            patientName: patientInfo.customer_given_name,
          });

          if (response.success && response.data?.conversation) {
            const conversation = response.data.conversation;
            const newConversationId =
              conversation.id || (conversation as { _id?: string })._id;
            setConversationId(newConversationId);
          } else {
            throw new Error(
              response.message || 'Failed to create conversation'
            );
          }
        } catch (error) {
          console.error('Error creating conversation:', error);
          toast({
            title: 'Chat Error',
            description:
              error instanceof Error
                ? error.message
                : 'Failed to create conversation',
            variant: 'destructive',
          });
          if (onClose) onClose();
        }
      };

      createConversationForPatient();
    }
  }, [
    mode,
    patientInfo,
    isOpen,
    conversationId,
    wsStatus.connected,
    createConversation,
    onClose,
    toast,
  ]);

  useEffect(() => {
    if (mode === 'fullscreen' && routeConversationId) {
      setConversationId(routeConversationId);
    }
  }, [mode, routeConversationId]);

  useEffect(() => {
    const shouldJoin = mode === 'fullscreen' ? true : isOpen;

    if (effectiveConversationId && wsStatus.connected && shouldJoin) {
      if (
        previousConversationIdRef.current &&
        previousConversationIdRef.current !== effectiveConversationId
      ) {
        leaveConversation(previousConversationIdRef.current);
      }

      joinConversation(effectiveConversationId);
      previousConversationIdRef.current = effectiveConversationId;
    }
  }, [
    effectiveConversationId,
    wsStatus.connected,
    isOpen,
    mode,
    joinConversation,
    leaveConversation,
  ]);

  useEffect(() => {
    return () => {
      if (previousConversationIdRef.current && wsStatus.connected) {
        leaveConversation(previousConversationIdRef.current);
      }
    };
  }, [wsStatus.connected, leaveConversation]);

  useEffect(() => {
    const shouldLoad = mode === 'fullscreen' ? true : isOpen;

    if (effectiveConversationId && wsStatus.connected && shouldLoad) {
      const loadMessages = async () => {
        try {
          await getMessages({
            conversationId: effectiveConversationId,
            page: 1,
            limit: 50,
          });
        } catch {
          toast({
            title: 'Connection Issue',
            description:
              'Failed to load messages. Please check your connection.',
            variant: 'destructive',
          });
        }
      };

      const timeoutId = setTimeout(loadMessages, 100);
      return () => clearTimeout(timeoutId);
    } else {
      if (
        effectiveConversationId &&
        !wsStatus.connected &&
        wsStatus.error &&
        !wsStatus.error.includes('Connection replaced by newer session')
      ) {
        toast({
          title: 'Connection Error',
          description: 'Chat connection lost. Trying to reconnect...',
          variant: 'destructive',
        });
      }
    }
  }, [
    effectiveConversationId,
    wsStatus.connected,
    wsStatus.error,
    isOpen,
    mode,
    getMessages,
    toast,
  ]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentMessages]);

  const [hasBeenOpened, setHasBeenOpened] = useState(false);

  useEffect(() => {
    const shouldBeOpen = mode === 'fullscreen' ? true : isOpen;
    if (shouldBeOpen && effectiveConversationId && !hasBeenOpened) {
      setHasBeenOpened(true);
    }
  }, [isOpen, mode, effectiveConversationId, hasBeenOpened]);

  useEffect(() => {
    setHasBeenOpened(false);
  }, [effectiveConversationId]);

  useEffect(() => {
    const shouldMarkAsRead =
      (mode === 'fullscreen' ? true : isOpen) &&
      effectiveConversationId &&
      currentMessages.length > 0;

    if (shouldMarkAsRead) {
      const hasUnreadMessages = currentMessages.some(
        msg => msg.senderId !== userId && msg.status !== 'read'
      );

      if (hasUnreadMessages) {
        markAsReadHook.markAsReadImmediately();
      }
    }
  }, [
    effectiveConversationId,
    isOpen,
    mode,
    currentMessages,
    markAsReadHook,
    userId,
  ]);

  useEffect(() => {
    const shouldSetup =
      (mode === 'fullscreen' ? true : isOpen) && hasBeenOpened;
    if (!effectiveConversationId || !shouldSetup) return;

    const timeoutId = setTimeout(() => {
      const messageElements = document.querySelectorAll('[data-message-id]');
      if (messageElements.length > 0) {
        markAsReadHook.setupMessageObserver(messageElements);
      }
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [
    effectiveConversationId,
    isOpen,
    mode,
    currentMessages,
    markAsReadHook,
    hasBeenOpened,
  ]);

  const handleTyping = useCallback(() => {
    if (!isTyping && effectiveConversationId) {
      setIsTyping(true);
      sendTypingIndicator(effectiveConversationId);
    }

    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    const timeout = setTimeout(() => {
      setIsTyping(false);
    }, 3000);

    setTypingTimeout(timeout);
  }, [isTyping, effectiveConversationId, sendTypingIndicator, typingTimeout]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !effectiveConversationId || isSendingMessage)
      return;

    try {
      await sendMessage({
        conversationId: effectiveConversationId,
        content: newMessage.trim(),
        type: 'text',
      });

      setNewMessage('');
      setIsTyping(false);
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
    handleTyping();
  };

  const handleClose = () => {
    setConversationId(null);
    setNewMessage('');
    setIsTyping(false);
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    if (onClose) onClose();
  };

  const handleBack = () => {
    navigate('/chat');
  };

  const isLoading =
    !wsStatus.connected ||
    isCreatingConversation ||
    (isLoadingMessages && currentMessages.length === 0);

  const displayPatientName = patientInfo?.customer_given_name || 'Patient';

  const error =
    errors[`conversation-${effectiveConversationId}`] ||
    errors[`messages-${effectiveConversationId}`];

  const chatState: ChatState = {
    newMessage,
    setNewMessage,
    isTyping,
    conversationId: effectiveConversationId,
    patientInfo,
    wsStatus,
    currentMessages,
    typingUsers,
    isLoading,
    isLoadingMessages,
    isCreatingConversation,
    isSendingMessage,
    error,
    handleSendMessage,
    handleKeyPress,
    handleInputChange,
    handleClose,
    handleBack,
    safeFormatDate,
    messagesEndRef,
    userId,
    userGivenName,
    displayPatientName,
  };

  return <>{children(chatState)}</>;
};
