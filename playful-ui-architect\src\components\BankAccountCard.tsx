import React from 'react';
import { MoreVertical, Loader2 } from 'lucide-react';
import { BankAccount } from '../types/bankAccount';
import { getBankIcon, formatAccountNumber } from '../utils/bankAccountUtils';

interface BankAccountCardProps {
  bankAccount: BankAccount;
  activeDropdown: boolean;
  isDeleting: boolean;
  onToggleDropdown: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

const BankAccountCard: React.FC<BankAccountCardProps> = ({
  bankAccount,
  activeDropdown,
  isDeleting,
  onToggleDropdown,
  onEdit,
  onDelete,
}) => {
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!activeDropdown) return;
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as Node;
      if (buttonRef.current && buttonRef.current.contains(target)) {
        return;
      }
      if (dropdownRef.current && dropdownRef.current.contains(target)) {
        return;
      }
      onToggleDropdown();
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown, onToggleDropdown]);

  return (
    <div className='mb-6'>
      <h2 className='text-lg font-semibold text-nursery-navy mb-2'>
        Your Bank Account
      </h2>
      <div className='bg-[#F2F2F2] border border-gray-300 rounded-lg p-4 flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <div
            className={`w-8 h-8 rounded ${getBankIcon(bankAccount.bank_name)} flex items-center justify-center`}
          >
            <span className='text-white text-xs font-bold'>
              {bankAccount.bank_name ? bankAccount.bank_name.charAt(0) : 'B'}
            </span>
          </div>
          <div>
            <div className='flex items-center gap-2'>
              <span className='font-medium text-gray-800'>
                {bankAccount.bank_name || 'Unknown Bank'}
              </span>
              <span className='text-gray-600'>
                - {formatAccountNumber(bankAccount.account_no || '')}
              </span>
            </div>
            <div className='text-sm text-gray-600 mt-1'>
              <div>{bankAccount.account_holder_name || 'Unknown'}</div>
              <div>{bankAccount.ifsc_code || 'Unknown IFSC'}</div>
            </div>
            <span
              className={`inline-block text-white text-xs px-3 py-1 rounded-full mt-2 ${
                bankAccount.status === 'active' ? 'bg-[#00A912]' : 'bg-gray-500'
              }`}
            >
              {bankAccount.status === 'active' ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
        <div className='relative'>
          <button
            ref={buttonRef}
            onClick={onToggleDropdown}
            className='p-2 hover:bg-gray-100 rounded-full'
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className='h-4 w-4 animate-spin text-gray-500' />
            ) : (
              <MoreVertical className='h-5 w-5 rounded-full text-nursery-navy' />
            )}
          </button>
          {activeDropdown && (
            <div
              ref={dropdownRef}
              className='absolute right-0 mt-1 w-36 bg-white border border-gray-200 rounded-lg shadow-lg z-10'
            >
              <button
                onClick={onEdit}
                className='w-full px-4 py-2 text-left text-sm font-semibold text-nursery-darkBlue hover:bg-gray-100 rounded-t-lg'
              >
                Edit
              </button>
              <button
                onClick={onDelete}
                className='w-full px-4 py-2 text-left text-sm font-semibold text-red-600 hover:bg-gray-100 rounded-b-lg'
              >
                Delete
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BankAccountCard;
