import React, { useState } from 'react';
import { <PERSON>, EyeOff, Loader2 } from 'lucide-react';
import {
  BankAccountForm as BankAccountFormType,
  ValidationErrors,
} from '../types/bankAccount';

interface BankAccountFormProps {
  formData: BankAccountFormType;
  errors: ValidationErrors;
  isEditing: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  onInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
}

const BankAccountForm: React.FC<BankAccountFormProps> = ({
  formData,
  errors,
  isEditing,
  isCreating,
  isUpdating,
  onInputChange,
  onSubmit,
  onCancel,
}) => {
  const [showAccountNumber, setShowAccountNumber] = useState<boolean>(false);

  return (
    <div className='bg-[#F2F2F2] border border-gray-200 rounded-lg shadow-lg p-6'>
      <h2 className='text-lg font-semibold text-gray-800 mb-4'>
        {isEditing ? 'Edit Bank Account' : 'Add Bank Account'}
      </h2>

      <form onSubmit={onSubmit} className='space-y-4'>
        {}
        <div>
          <label
            htmlFor='accountHolderName'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            Account Holder Name *
          </label>
          <input
            type='text'
            id='accountHolderName'
            name='accountHolderName'
            value={formData.accountHolderName}
            onChange={onInputChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
            placeholder='Enter account holder name'
          />
          {errors.accountHolderName && (
            <p className='text-red-500 text-xs mt-1'>
              {errors.accountHolderName}
            </p>
          )}
        </div>

        {}
        <div>
          <label
            htmlFor='accountNumber'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            Account Number *
          </label>
          <div className='relative'>
            <input
              type={showAccountNumber ? 'text' : 'password'}
              id='accountNumber'
              name='accountNumber'
              value={formData.accountNumber}
              onChange={onInputChange}
              className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
              placeholder='Enter account number'
            />
            <button
              type='button'
              onClick={() => setShowAccountNumber(!showAccountNumber)}
              className='absolute right-3 top-1/2 transform -translate-y-1/2'
            >
              {showAccountNumber ? (
                <EyeOff className='h-4 w-4 text-gray-500' />
              ) : (
                <Eye className='h-4 w-4 text-gray-500' />
              )}
            </button>
          </div>
          {errors.accountNumber && (
            <p className='text-red-500 text-xs mt-1'>{errors.accountNumber}</p>
          )}
        </div>

        {}
        <div>
          <label
            htmlFor='confirmAccountNumber'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            Confirm Account Number *
          </label>
          <input
            type='text'
            id='confirmAccountNumber'
            name='confirmAccountNumber'
            value={formData.confirmAccountNumber}
            onChange={onInputChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
            placeholder='Re-enter account number'
          />
          {errors.confirmAccountNumber && (
            <p className='text-red-500 text-xs mt-1'>
              {errors.confirmAccountNumber}
            </p>
          )}
        </div>

        {}
        <div>
          <label
            htmlFor='bankName'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            Bank Name *
          </label>
          <input
            type='text'
            id='bankName'
            name='bankName'
            value={formData.bankName}
            onChange={onInputChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
            placeholder='Enter bank name'
          />
          {errors.bankName && (
            <p className='text-red-500 text-xs mt-1'>{errors.bankName}</p>
          )}
        </div>

        {}
        <div>
          <label
            htmlFor='branch'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            Branch *
          </label>
          <input
            type='text'
            id='branch'
            name='branch'
            value={formData.branch}
            onChange={onInputChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
            placeholder='Enter branch name'
          />
          {errors.branch && (
            <p className='text-red-500 text-xs mt-1'>{errors.branch}</p>
          )}
        </div>

        {}
        <div>
          <label
            htmlFor='ifscCode'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            IFSC Code *
          </label>
          <input
            type='text'
            id='ifscCode'
            name='ifscCode'
            value={formData.ifscCode}
            onChange={onInputChange}
            maxLength={11}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
            placeholder='Enter IFSC code (e.g., SBIN0001234)'
          />
          {errors.ifscCode && (
            <p className='text-red-500 text-xs mt-1'>{errors.ifscCode}</p>
          )}
        </div>

        {}
        <div>
          <label
            htmlFor='address'
            className='block text-sm font-medium text-gray-700 mb-1'
          >
            Branch Address *
          </label>
          <textarea
            id='address'
            name='address'
            value={formData.address}
            onChange={onInputChange}
            rows={3}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent resize-none'
            placeholder='Enter complete branch address'
          />
          {errors.address && (
            <p className='text-red-500 text-xs mt-1'>{errors.address}</p>
          )}
        </div>

        {}
        <div className='flex items-start gap-3 pt-2'>
          <input
            type='checkbox'
            id='confirmTerms'
            name='confirmTerms'
            checked={formData.confirmTerms}
            onChange={onInputChange}
            className='mt-1 h-4 w-4 accent-nursery-blue focus:ring-nursery-blue border-gray-300 rounded'
          />
          <label htmlFor='confirmTerms' className='text-sm text-gray-700'>
            I confirm that the bank account details provided are accurate and I
            authorize the use of this account for transactions.
          </label>
        </div>
        {errors.confirmTerms && (
          <p className='text-red-500 text-xs mt-1'>{errors.confirmTerms}</p>
        )}

        {}
        <div className='flex gap-3 pt-4 justify-center'>
          <button
            type='submit'
            disabled={!formData.confirmTerms || isCreating || isUpdating}
            className=' bg-nursery-blue text-white py-2 px-4 rounded-md hover:bg-nursery-darkBlue transition-colors duration-200 font-medium disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2'
          >
            {(isCreating || isUpdating) && (
              <Loader2 className='h-4 w-4 animate-spin' />
            )}
            {isCreating || isUpdating
              ? isEditing
                ? 'Updating...'
                : 'Adding...'
              : isEditing
                ? 'Update Account'
                : 'Add Account'}
          </button>
          <button
            type='button'
            onClick={onCancel}
            disabled={isCreating || isUpdating}
            className=' bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium disabled:cursor-not-allowed'
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default BankAccountForm;
